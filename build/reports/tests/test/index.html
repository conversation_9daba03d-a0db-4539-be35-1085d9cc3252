<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - Test Summary</title>
<link href="css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="css/style.css" rel="stylesheet" type="text/css"/>
<script src="js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Test Summary</h1>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">29</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.057s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div class="tab-container">
<ul class="tabLinks">
<li>
<a href="#">Packages</a>
</li>
<li>
<a href="#">Classes</a>
</li>
</ul>
<div class="tab">
<h2>Packages</h2>
<table>
<thead>
<tr>
<th>Package</th>
<th>Tests</th>
<th>Failures</th>
<th>Ignored</th>
<th>Duration</th>
<th>Success rate</th>
</tr>
</thead>
<tbody>
<tr>
<td class="success">
<a href="packages/com.aicodingcli.code.html">com.aicodingcli.code</a>
</td>
<td>2</td>
<td>0</td>
<td>0</td>
<td>0.042s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="packages/com.aicodingcli.code.analysis.html">com.aicodingcli.code.analysis</a>
</td>
<td>11</td>
<td>0</td>
<td>0</td>
<td>0.007s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="packages/com.aicodingcli.code.common.html">com.aicodingcli.code.common</a>
</td>
<td>5</td>
<td>0</td>
<td>0</td>
<td>0.001s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="packages/com.aicodingcli.code.metrics.html">com.aicodingcli.code.metrics</a>
</td>
<td>10</td>
<td>0</td>
<td>0</td>
<td>0.007s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="packages/com.aicodingcli.code.quality.html">com.aicodingcli.code.quality</a>
</td>
<td>1</td>
<td>0</td>
<td>0</td>
<td>0s</td>
<td class="success">100%</td>
</tr>
</tbody>
</table>
</div>
<div class="tab">
<h2>Classes</h2>
<table>
<thead>
<tr>
<th>Class</th>
<th>Tests</th>
<th>Failures</th>
<th>Ignored</th>
<th>Duration</th>
<th>Success rate</th>
</tr>
</thead>
<tbody>
<tr>
<td class="success">
<a href="classes/com.aicodingcli.code.DebugTest.html">com.aicodingcli.code.DebugTest</a>
</td>
<td>1</td>
<td>0</td>
<td>0</td>
<td>0.035s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.aicodingcli.code.ProjectAnalysisDebugTest.html">com.aicodingcli.code.ProjectAnalysisDebugTest</a>
</td>
<td>1</td>
<td>0</td>
<td>0</td>
<td>0.007s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.aicodingcli.code.analysis.CodeAnalysisModelsTest.html">com.aicodingcli.code.analysis.CodeAnalysisModelsTest</a>
</td>
<td>5</td>
<td>0</td>
<td>0</td>
<td>0.002s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.aicodingcli.code.analysis.CodeAnalyzerTest.html">com.aicodingcli.code.analysis.CodeAnalyzerTest</a>
</td>
<td>6</td>
<td>0</td>
<td>0</td>
<td>0.005s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.aicodingcli.code.common.ProgrammingLanguageTest.html">com.aicodingcli.code.common.ProgrammingLanguageTest</a>
</td>
<td>5</td>
<td>0</td>
<td>0</td>
<td>0.001s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.aicodingcli.code.metrics.ImprovedMetricsCalculatorTest.html">com.aicodingcli.code.metrics.ImprovedMetricsCalculatorTest</a>
</td>
<td>8</td>
<td>0</td>
<td>0</td>
<td>0.004s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.aicodingcli.code.metrics.MetricsDebugTest.html">com.aicodingcli.code.metrics.MetricsDebugTest</a>
</td>
<td>2</td>
<td>0</td>
<td>0</td>
<td>0.003s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.aicodingcli.code.quality.QualityAnalyzerTest.html">com.aicodingcli.code.quality.QualityAnalyzerTest</a>
</td>
<td>1</td>
<td>0</td>
<td>0</td>
<td>0s</td>
<td class="success">100%</td>
</tr>
</tbody>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="https://www.gradle.org">Gradle 8.14.1</a> at 2025年6月18日 00:04:46</p>
</div>
</div>
</body>
</html>

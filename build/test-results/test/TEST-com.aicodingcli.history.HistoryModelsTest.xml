<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.aicodingcli.history.HistoryModelsTest" tests="16" skipped="0" failures="0" errors="0" timestamp="2025-06-17T13:34:31.395Z" hostname="zxnapdeMacBook-Pro.local" time="0.004">
  <properties/>
  <testcase name="should create ConversationMessage with token usage()" classname="com.aicodingcli.history.HistoryModelsTest" time="0.0"/>
  <testcase name="should create ConversationMessage with default values()" classname="com.aicodingcli.history.HistoryModelsTest" time="0.0"/>
  <testcase name="should handle null values in HistoryStatistics()" classname="com.aicodingcli.history.HistoryModelsTest" time="0.0"/>
  <testcase name="should create HistoryStatistics correctly()" classname="com.aicodingcli.history.HistoryModelsTest" time="0.001"/>
  <testcase name="should get last assistant message()" classname="com.aicodingcli.history.HistoryModelsTest" time="0.0"/>
  <testcase name="should return null when no assistant message exists()" classname="com.aicodingcli.history.HistoryModelsTest" time="0.0"/>
  <testcase name="should create ConversationSession with default values()" classname="com.aicodingcli.history.HistoryModelsTest" time="0.0"/>
  <testcase name="should create HistorySearchCriteria with default values()" classname="com.aicodingcli.history.HistoryModelsTest" time="0.001"/>
  <testcase name="should get last user message()" classname="com.aicodingcli.history.HistoryModelsTest" time="0.0"/>
  <testcase name="should create HistorySearchCriteria with custom values()" classname="com.aicodingcli.history.HistoryModelsTest" time="0.0"/>
  <testcase name="should return null when no user message exists()" classname="com.aicodingcli.history.HistoryModelsTest" time="0.0"/>
  <testcase name="should validate message content is not blank()" classname="com.aicodingcli.history.HistoryModelsTest" time="0.001"/>
  <testcase name="should generate summary for empty conversation()" classname="com.aicodingcli.history.HistoryModelsTest" time="0.001"/>
  <testcase name="should generate conversation summary()" classname="com.aicodingcli.history.HistoryModelsTest" time="0.0"/>
  <testcase name="should add message to conversation()" classname="com.aicodingcli.history.HistoryModelsTest" time="0.0"/>
  <testcase name="should create MessageTokenUsage correctly()" classname="com.aicodingcli.history.HistoryModelsTest" time="0.0"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>

<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.aicodingcli.ai.AiServiceTest" tests="4" skipped="0" failures="0" errors="0" timestamp="2025-06-17T13:34:31.223Z" hostname="zxnapdeMacBook-Pro.local" time="0.006">
  <properties/>
  <testcase name="should support multiple AI providers()" classname="com.aicodingcli.ai.AiServiceTest" time="0.003"/>
  <testcase name="should validate model parameters()" classname="com.aicodingcli.ai.AiServiceTest" time="0.001"/>
  <testcase name="should create AI service with valid configuration()" classname="com.aicodingcli.ai.AiServiceTest" time="0.001"/>
  <testcase name="should throw exception for invalid API key()" classname="com.aicodingcli.ai.AiServiceTest" time="0.001"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>

<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.aicodingcli.ai.providers.OllamaServiceTest" tests="8" skipped="0" failures="0" errors="0" timestamp="2025-06-17T13:34:31.331Z" hostname="zxnapdeMacBook-Pro.local" time="0.021">
  <properties/>
  <testcase name="should use custom base URL if provided()" classname="com.aicodingcli.ai.providers.OllamaServiceTest" time="0.004"/>
  <testcase name="should handle temperature and max tokens options()" classname="com.aicodingcli.ai.providers.OllamaServiceTest" time="0.003"/>
  <testcase name="should test connection successfully()" classname="com.aicodingcli.ai.providers.OllamaServiceTest" time="0.003"/>
  <testcase name="should fail connection test on error()" classname="com.aicodingcli.ai.providers.OllamaServiceTest" time="0.002"/>
  <testcase name="should handle streaming chat request()" classname="com.aicodingcli.ai.providers.OllamaServiceTest" time="0.001"/>
  <testcase name="should validate request before sending()" classname="com.aicodingcli.ai.providers.OllamaServiceTest" time="0.001"/>
  <testcase name="should make successful chat request()" classname="com.aicodingcli.ai.providers.OllamaServiceTest" time="0.003"/>
  <testcase name="should handle API error response()" classname="com.aicodingcli.ai.providers.OllamaServiceTest" time="0.003"/>
  <system-out><![CDATA[21:34:31.332 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#15
21:34:31.334 [Test worker @kotlinx.coroutines.test runner#97] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #15#16
21:34:31.334 [Test worker @kotlinx.coroutines.test runner#97] DEBUG io.mockk.impl.recording.states.AnsweringState -- Answering HttpResponse(status=200 OK, body={
    "model": "llama2",
    "created_at": "2023-12-12T14:13:43.416799Z",
    "message": {
        "role": "assistant",
        "content": "Hi"
    },
    "done": true,
    "total_duration": 1000000,
    "eval_count": 1,
    "prompt_eval_count": 1
}, headers={}) on <EMAIL>(http://custom-ollama:11434/api/chat, {"model":"llama2","messages":[{"role":"user","content":"Hello"}],"stream":false,"options":{"temperature":0.7,"num_predict":1000}}, {Content-Type=application/json}, continuation {})
21:34:31.336 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#17
21:34:31.337 [Test worker @kotlinx.coroutines.test runner#103] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #17#18
21:34:31.337 [Test worker @kotlinx.coroutines.test runner#103] DEBUG io.mockk.impl.recording.states.AnsweringState -- Answering HttpResponse(status=200 OK, body={
    "model": "llama2",
    "created_at": "2023-12-12T14:13:43.416799Z",
    "message": {
        "role": "assistant",
        "content": "Hello there!"
    },
    "done": true,
    "eval_count": 2,
    "prompt_eval_count": 1
}, headers={}) on <EMAIL>(http://localhost:11434/api/chat, {"model":"llama2","messages":[{"role":"user","content":"Hello"}],"stream":false,"options":{"temperature":0.5,"num_predict":500}}, {Content-Type=application/json}, continuation {})
21:34:31.339 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#19
21:34:31.341 [Test worker @kotlinx.coroutines.test runner#109] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #19#20
21:34:31.341 [Test worker @kotlinx.coroutines.test runner#109] DEBUG io.mockk.impl.recording.states.AnsweringState -- Answering HttpResponse(status=200 OK, body={
    "models": [
        {
            "name": "llama2:latest",
            "model": "llama2:latest",
            "size": 3825819519,
            "digest": "fe938a131f40e6f6d40083c9f0f430a515233eb2edaa6d72eb85c50d64f2300e",
            "details": {
                "format": "gguf",
                "family": "llama",
                "parameter_size": "7B",
                "quantization_level": "Q4_0"
            }
        }
    ]
}, headers={Content-Type=application/json}) on <EMAIL>(http://localhost:11434/api/tags, {Content-Type=application/json}, continuation {})
21:34:31.342 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#21
21:34:31.343 [Test worker @kotlinx.coroutines.test runner#113] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #21#22
21:34:31.343 [Test worker @kotlinx.coroutines.test runner#113] DEBUG io.mockk.impl.recording.states.AnsweringState -- Throwing com.aicodingcli.http.HttpException: HTTP error 503: Service <NAME_EMAIL>(http://localhost:11434/api/tags, {Content-Type=application/json}, continuation {})
21:34:31.344 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#23
21:34:31.345 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#24
21:34:31.346 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#25
21:34:31.348 [Test worker @kotlinx.coroutines.test runner#121] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #25#26
21:34:31.348 [Test worker @kotlinx.coroutines.test runner#121] DEBUG io.mockk.impl.recording.states.AnsweringState -- Answering HttpResponse(status=200 OK, body={
    "model": "llama2",
    "created_at": "2023-12-12T14:13:43.416799Z",
    "message": {
        "role": "assistant",
        "content": "Hello! How can I help you today?"
    },
    "done": true,
    "total_duration": 5191566416,
    "load_duration": 2154458,
    "prompt_eval_count": 26,
    "prompt_eval_duration": 383809000,
    "eval_count": 298,
    "eval_duration": 4799921000
}, headers={Content-Type=application/json}) on <EMAIL>(http://localhost:11434/api/chat, {"model":"llama2","messages":[{"role":"user","content":"Hello, Ollama!"}],"stream":false,"options":{"temperature":0.7,"num_predict":1000}}, {Content-Type=application/json}, continuation {})
21:34:31.350 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#27
21:34:31.351 [Test worker @kotlinx.coroutines.test runner#127] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #27#28
21:34:31.352 [Test worker @kotlinx.coroutines.test runner#127] DEBUG io.mockk.impl.recording.states.AnsweringState -- Throwing com.aicodingcli.http.HttpException: HTTP error 404: Not <NAME_EMAIL>(http://localhost:11434/api/chat, {"model":"llama2","messages":[{"role":"user","content":"Hello"}],"stream":false,"options":{"temperature":0.7,"num_predict":1000}}, {Content-Type=application/json}, continuation {})
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>

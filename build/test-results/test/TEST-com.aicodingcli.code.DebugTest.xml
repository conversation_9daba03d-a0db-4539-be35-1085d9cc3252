<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.aicodingcli.code.DebugTest" tests="1" skipped="0" failures="0" errors="0" timestamp="2025-06-17T16:04:46.443Z" hostname="zxnapdeMacBook-Pro.local" time="0.035">
  <properties/>
  <testcase name="debug complex code analysis()" classname="com.aicodingcli.code.DebugTest" time="0.035"/>
  <system-out><![CDATA[=== DEBUG INFO ===
File path: /var/folders/58/mctmb5rs4m36zm0x9zdhfy480000gn/T/junit13379751467343580447/ComplexClass.kt
Language: KOTLIN
Lines of code: 17
Cyclomatic complexity: 4
Maintainability index: 77.83393327971892
Number of issues: 1
Number of suggestions: 2

Issues:
- CODE_SMELL: Magic number '10' should be replaced with a named constant

Suggestions:
- MAINTAINABILITY: Consider extracting complex conditional logic into separate methods
- READABILITY: Consider adding documentation for public methods

=== DIRECT METHOD CALLS ===
Direct suggestions count: 2
- MAINTAINABILITY: Consider extracting complex conditional logic into separate methods
- READABILITY: Consider adding documentation for public methods
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>

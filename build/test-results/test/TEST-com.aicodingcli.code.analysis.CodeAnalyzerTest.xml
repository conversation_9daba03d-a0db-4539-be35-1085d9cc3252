<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.aicodingcli.code.analysis.CodeAnalyzerTest" tests="1" skipped="0" failures="1" errors="0" timestamp="2025-06-17T14:32:03.070Z" hostname="zxnapdeMacBook-Pro.local" time="0.042">
  <properties/>
  <testcase name="should suggest improvements for complex code()" classname="com.aicodingcli.code.analysis.CodeAnalyzerTest" time="0.042">
    <failure message="org.opentest4j.AssertionFailedError: expected: &lt;true&gt; but was: &lt;false&gt;" type="org.opentest4j.AssertionFailedError">org.opentest4j.AssertionFailedError: expected: &lt;true&gt; but was: &lt;false&gt;
	at app//org.junit.jupiter.api.AssertionFailureBuilder.build(AssertionFailureBuilder.java:151)
	at app//org.junit.jupiter.api.AssertionFailureBuilder.buildAndThrow(AssertionFailureBuilder.java:132)
	at app//org.junit.jupiter.api.AssertTrue.failNotTrue(AssertTrue.java:63)
	at app//org.junit.jupiter.api.AssertTrue.assertTrue(AssertTrue.java:36)
	at app//org.junit.jupiter.api.AssertTrue.assertTrue(AssertTrue.java:31)
	at app//org.junit.jupiter.api.Assertions.assertTrue(Assertions.java:183)
	at app//com.aicodingcli.code.analysis.CodeAnalyzerTest$should suggest improvements for complex code$1.invokeSuspend(CodeAnalyzerTest.kt:85)
	at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at app//kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at app//kotlinx.coroutines.EventLoopImplBase.processNextEvent(EventLoop.common.kt:277)
	at app//kotlinx.coroutines.BlockingCoroutine.joinBlocking(Builders.kt:95)
	at app//kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking(Builders.kt:69)
	at app//kotlinx.coroutines.BuildersKt.runBlocking(Unknown Source)
	at app//kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking$default(Builders.kt:48)
	at app//kotlinx.coroutines.BuildersKt.runBlocking$default(Unknown Source)
	at app//com.aicodingcli.code.analysis.CodeAnalyzerTest.should suggest improvements for complex code(CodeAnalyzerTest.kt:61)
	at java.base@23.0.2/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base@23.0.2/java.util.ArrayList.forEach(ArrayList.java:1597)
	at java.base@23.0.2/java.util.ArrayList.forEach(ArrayList.java:1597)
</failure>
  </testcase>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>

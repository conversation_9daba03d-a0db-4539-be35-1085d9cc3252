<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.aicodingcli.history.HistoryManagerTest" tests="18" skipped="0" failures="0" errors="0" timestamp="2025-06-17T15:18:04.469Z" hostname="zxnapdeMacBook-Pro.local" time="0.235">
  <properties/>
  <testcase name="should handle empty message content validation()" classname="com.aicodingcli.history.HistoryManagerTest" time="0.001"/>
  <testcase name="should persist conversations to file()" classname="com.aicodingcli.history.HistoryManagerTest" time="0.001"/>
  <testcase name="should return false when deleting non-existent conversation()" classname="com.aicodingcli.history.HistoryManagerTest" time="0.0"/>
  <testcase name="should get all conversations sorted by update time()" classname="com.aicodingcli.history.HistoryManagerTest" time="0.216"/>
  <testcase name="should throw exception for ambiguous partial ID()" classname="com.aicodingcli.history.HistoryManagerTest" time="0.002"/>
  <testcase name="should get conversation by partial ID()" classname="com.aicodingcli.history.HistoryManagerTest" time="0.0"/>
  <testcase name="should get conversation by exact ID()" classname="com.aicodingcli.history.HistoryManagerTest" time="0.0"/>
  <testcase name="should calculate statistics correctly()" classname="com.aicodingcli.history.HistoryManagerTest" time="0.002"/>
  <testcase name="should search conversations by model()" classname="com.aicodingcli.history.HistoryManagerTest" time="0.001"/>
  <testcase name="should search conversations by query()" classname="com.aicodingcli.history.HistoryManagerTest" time="0.002"/>
  <testcase name="should return null for non-existent conversation ID()" classname="com.aicodingcli.history.HistoryManagerTest" time="0.0"/>
  <testcase name="should add message to conversation successfully()" classname="com.aicodingcli.history.HistoryManagerTest" time="0.0"/>
  <testcase name="should clear all conversations()" classname="com.aicodingcli.history.HistoryManagerTest" time="0.002"/>
  <testcase name="should create new conversation successfully()" classname="com.aicodingcli.history.HistoryManagerTest" time="0.0"/>
  <testcase name="should throw exception when adding message to non-existent conversation()" classname="com.aicodingcli.history.HistoryManagerTest" time="0.001"/>
  <testcase name="should search conversations by provider()" classname="com.aicodingcli.history.HistoryManagerTest" time="0.001"/>
  <testcase name="should limit search results()" classname="com.aicodingcli.history.HistoryManagerTest" time="0.002"/>
  <testcase name="should delete conversation successfully()" classname="com.aicodingcli.history.HistoryManagerTest" time="0.0"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>

<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.aicodingcli.code.analysis.CodeAnalysisModelsTest" tests="5" skipped="0" failures="0" errors="0" timestamp="2025-06-17T16:04:46.487Z" hostname="zxnapdeMacBook-Pro.local" time="0.002">
  <properties/>
  <testcase name="should create code issue with all properties()" classname="com.aicodingcli.code.analysis.CodeAnalysisModelsTest" time="0.001"/>
  <testcase name="should create code analysis result()" classname="com.aicodingcli.code.analysis.CodeAnalysisModelsTest" time="0.0"/>
  <testcase name="should create dependency information()" classname="com.aicodingcli.code.analysis.CodeAnalysisModelsTest" time="0.001"/>
  <testcase name="should create improvement suggestion()" classname="com.aicodingcli.code.analysis.CodeAnalysisModelsTest" time="0.0"/>
  <testcase name="should create code metrics with valid values()" classname="com.aicodingcli.code.analysis.CodeAnalysisModelsTest" time="0.0"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>

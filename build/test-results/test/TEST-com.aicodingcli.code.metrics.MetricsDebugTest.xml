<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.aicodingcli.code.metrics.MetricsDebugTest" tests="2" skipped="0" failures="0" errors="0" timestamp="2025-06-17T16:04:46.506Z" hostname="zxnapdeMacBook-Pro.local" time="0.003">
  <properties/>
  <testcase name="debug lines of code calculation()" classname="com.aicodingcli.code.metrics.MetricsDebugTest" time="0.003"/>
  <testcase name="debug cyclomatic complexity calculation()" classname="com.aicodingcli.code.metrics.MetricsDebugTest" time="0.0"/>
  <system-out><![CDATA[=== LINES OF CODE DEBUG ===
Code:
// This is a comment
class TestClass {
    // Another comment
    
    fun method1() {
        // Method comment
        val x = 1 // Inline comment
        return x
    }
    
    /*
     * Block comment
     * Multiple lines
     */
    fun method2() {
        val y = 2
        return y
    }
}

Calculated LOC: 10
Expected LOC: 7 (class declaration, 2 method declarations, 4 statements)
Line 1: '// This is a comment' -> SKIP
Line 2: 'class TestClass {' -> CODE
Line 3: '// Another comment' -> SKIP
Line 4: '' -> SKIP
Line 5: 'fun method1() {' -> CODE
Line 6: '// Method comment' -> SKIP
Line 7: 'val x = 1 // Inline comment' -> CODE
Line 8: 'return x' -> CODE
Line 9: '}' -> CODE
Line 10: '' -> SKIP
Line 11: '/*' -> SKIP
Line 12: '* Block comment' -> SKIP
Line 13: '* Multiple lines' -> SKIP
Line 14: '*/' -> SKIP
Line 15: 'fun method2() {' -> CODE
Line 16: 'val y = 2' -> CODE
Line 17: 'return y' -> CODE
Line 18: '}' -> CODE
Line 19: '}' -> CODE
=== CYCLOMATIC COMPLEXITY DEBUG ===
Code:
class ConditionalClass {
    fun processInput(input: String?): String {
        if (input == null) {
            return "null"
        }
        
        if (input.isEmpty()) {
            return "empty"
        }
        
        return when {
            input.length > 10 -> "long"
            input.length > 5 -> "medium"
            else -> "short"
        }
    }
}

Calculated complexity: 5
Expected complexity: 6 (2 if statements + 3 when branches + 1 base)
Lines of code: 15
Duplicated lines: 0
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>

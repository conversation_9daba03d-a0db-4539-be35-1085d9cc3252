<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.aicodingcli.AnalyzeCommandTest" tests="6" skipped="0" failures="0" errors="0" timestamp="2025-06-17T15:18:03.923Z" hostname="zxnapdeMacBook-Pro.local" time="0.029">
  <properties/>
  <testcase name="should handle analyze issues command()" classname="com.aicodingcli.AnalyzeCommandTest" time="0.014"/>
  <testcase name="should handle json format output()" classname="com.aicodingcli.AnalyzeCommandTest" time="0.004"/>
  <testcase name="should handle analyze file command()" classname="com.aicodingcli.AnalyzeCommandTest" time="0.003"/>
  <testcase name="should handle analyze help command()" classname="com.aicodingcli.AnalyzeCommandTest" time="0.001"/>
  <testcase name="should handle analyze project command()" classname="com.aicodingcli.AnalyzeCommandTest" time="0.004"/>
  <testcase name="should handle analyze metrics command()" classname="com.aicodingcli.AnalyzeCommandTest" time="0.002"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>

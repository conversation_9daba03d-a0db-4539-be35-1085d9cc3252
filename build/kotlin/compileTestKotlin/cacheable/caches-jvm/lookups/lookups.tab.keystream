  AiCodingCli com.aicodingcli  AiCodingCliTest com.aicodingcli  AnalyzeCommandTest com.aicodingcli  ByteArrayOutputStream com.aicodingcli  File com.aicodingcli  PrintStream com.aicodingcli  System com.aicodingcli  TempDir com.aicodingcli  Test com.aicodingcli  arrayOf com.aicodingcli  assertEquals com.aicodingcli  
assertTrue com.aicodingcli  contains com.aicodingcli  trim com.aicodingcli  
trimIndent com.aicodingcli  	writeText com.aicodingcli  run com.aicodingcli.AiCodingCli  AiCodingCli com.aicodingcli.AiCodingCliTest  ByteArrayOutputStream com.aicodingcli.AiCodingCliTest  PrintStream com.aicodingcli.AiCodingCliTest  System com.aicodingcli.AiCodingCliTest  arrayOf com.aicodingcli.AiCodingCliTest  assertEquals com.aicodingcli.AiCodingCliTest  
assertTrue com.aicodingcli.AiCodingCliTest  contains com.aicodingcli.AiCodingCliTest  trim com.aicodingcli.AiCodingCliTest  
trimIndent com.aicodingcli.AiCodingCliTest  AiCodingCli "com.aicodingcli.AnalyzeCommandTest  ByteArrayOutputStream "com.aicodingcli.AnalyzeCommandTest  File "com.aicodingcli.AnalyzeCommandTest  PrintStream "com.aicodingcli.AnalyzeCommandTest  System "com.aicodingcli.AnalyzeCommandTest  arrayOf "com.aicodingcli.AnalyzeCommandTest  
assertTrue "com.aicodingcli.AnalyzeCommandTest  contains "com.aicodingcli.AnalyzeCommandTest  tempDir "com.aicodingcli.AnalyzeCommandTest  
trimIndent "com.aicodingcli.AnalyzeCommandTest  	writeText "com.aicodingcli.AnalyzeCommandTest  AiHttpClient com.aicodingcli.ai  	AiMessage com.aicodingcli.ai  
AiProvider com.aicodingcli.ai  	AiRequest com.aicodingcli.ai  AiRequestResponseTest com.aicodingcli.ai  
AiResponse com.aicodingcli.ai  	AiService com.aicodingcli.ai  AiServiceConfig com.aicodingcli.ai  AiServiceFactory com.aicodingcli.ai  
AiServiceTest com.aicodingcli.ai  
AiStreamChunk com.aicodingcli.ai  
BeforeEach com.aicodingcli.ai  ClaudeException com.aicodingcli.ai  
ClaudeService com.aicodingcli.ai  FinishReason com.aicodingcli.ai  HttpResponse com.aicodingcli.ai  HttpStatusCode com.aicodingcli.ai  IllegalArgumentException com.aicodingcli.ai  MessageRole com.aicodingcli.ai  OllamaException com.aicodingcli.ai  
OllamaService com.aicodingcli.ai  OpenAiException com.aicodingcli.ai  
OpenAiService com.aicodingcli.ai  Test com.aicodingcli.ai  
TokenUsage com.aicodingcli.ai  assertDoesNotThrow com.aicodingcli.ai  assertEquals com.aicodingcli.ai  assertFalse com.aicodingcli.ai  
assertNotNull com.aicodingcli.ai  assertThrows com.aicodingcli.ai  
assertTrue com.aicodingcli.ai  
claudeService com.aicodingcli.ai  coEvery com.aicodingcli.ai  coVerify com.aicodingcli.ai  com com.aicodingcli.ai  config com.aicodingcli.ai  contains com.aicodingcli.ai  
createService com.aicodingcli.ai  	emptyList com.aicodingcli.ai  kotlinx com.aicodingcli.ai  listOf com.aicodingcli.ai  mapOf com.aicodingcli.ai  mockHttpClient com.aicodingcli.ai  mockk com.aicodingcli.ai  
mutableListOf com.aicodingcli.ai  
ollamaService com.aicodingcli.ai  
openAiService com.aicodingcli.ai  runTest com.aicodingcli.ai  to com.aicodingcli.ai  
trimIndent com.aicodingcli.ai  content com.aicodingcli.ai.AiMessage  role com.aicodingcli.ai.AiMessage  CLAUDE com.aicodingcli.ai.AiProvider  OLLAMA com.aicodingcli.ai.AiProvider  OPENAI com.aicodingcli.ai.AiProvider  to com.aicodingcli.ai.AiProvider  	maxTokens com.aicodingcli.ai.AiRequest  messages com.aicodingcli.ai.AiRequest  model com.aicodingcli.ai.AiRequest  temperature com.aicodingcli.ai.AiRequest  	AiMessage (com.aicodingcli.ai.AiRequestResponseTest  	AiRequest (com.aicodingcli.ai.AiRequestResponseTest  
AiResponse (com.aicodingcli.ai.AiRequestResponseTest  
AiStreamChunk (com.aicodingcli.ai.AiRequestResponseTest  FinishReason (com.aicodingcli.ai.AiRequestResponseTest  MessageRole (com.aicodingcli.ai.AiRequestResponseTest  
TokenUsage (com.aicodingcli.ai.AiRequestResponseTest  assertEquals (com.aicodingcli.ai.AiRequestResponseTest  assertThrows (com.aicodingcli.ai.AiRequestResponseTest  coEvery (com.aicodingcli.ai.AiRequestResponseTest  kotlinx (com.aicodingcli.ai.AiRequestResponseTest  listOf (com.aicodingcli.ai.AiRequestResponseTest  mockk (com.aicodingcli.ai.AiRequestResponseTest  
mutableListOf (com.aicodingcli.ai.AiRequestResponseTest  runTest (com.aicodingcli.ai.AiRequestResponseTest  content com.aicodingcli.ai.AiResponse  finishReason com.aicodingcli.ai.AiResponse  model com.aicodingcli.ai.AiResponse  usage com.aicodingcli.ai.AiResponse  
streamChat com.aicodingcli.ai.AiService  apiKey "com.aicodingcli.ai.AiServiceConfig  copy "com.aicodingcli.ai.AiServiceConfig  model "com.aicodingcli.ai.AiServiceConfig  provider "com.aicodingcli.ai.AiServiceConfig  temperature "com.aicodingcli.ai.AiServiceConfig  
createService #com.aicodingcli.ai.AiServiceFactory  
AiProvider  com.aicodingcli.ai.AiServiceTest  AiServiceConfig  com.aicodingcli.ai.AiServiceTest  AiServiceFactory  com.aicodingcli.ai.AiServiceTest  assertDoesNotThrow  com.aicodingcli.ai.AiServiceTest  assertThrows  com.aicodingcli.ai.AiServiceTest  
createService  com.aicodingcli.ai.AiServiceTest  runTest  com.aicodingcli.ai.AiServiceTest  content  com.aicodingcli.ai.AiStreamChunk  finishReason  com.aicodingcli.ai.AiStreamChunk  STOP com.aicodingcli.ai.FinishReason  	ASSISTANT com.aicodingcli.ai.MessageRole  SYSTEM com.aicodingcli.ai.MessageRole  USER com.aicodingcli.ai.MessageRole  completionTokens com.aicodingcli.ai.TokenUsage  promptTokens com.aicodingcli.ai.TokenUsage  totalTokens com.aicodingcli.ai.TokenUsage  AiHttpClient com.aicodingcli.ai.providers  	AiMessage com.aicodingcli.ai.providers  
AiProvider com.aicodingcli.ai.providers  	AiRequest com.aicodingcli.ai.providers  AiServiceConfig com.aicodingcli.ai.providers  
BeforeEach com.aicodingcli.ai.providers  ClaudeException com.aicodingcli.ai.providers  
ClaudeService com.aicodingcli.ai.providers  ClaudeServiceTest com.aicodingcli.ai.providers  FinishReason com.aicodingcli.ai.providers  HttpResponse com.aicodingcli.ai.providers  HttpStatusCode com.aicodingcli.ai.providers  IllegalArgumentException com.aicodingcli.ai.providers  MessageRole com.aicodingcli.ai.providers  OllamaException com.aicodingcli.ai.providers  
OllamaService com.aicodingcli.ai.providers  OllamaServiceTest com.aicodingcli.ai.providers  OpenAiException com.aicodingcli.ai.providers  
OpenAiService com.aicodingcli.ai.providers  OpenAiServiceTest com.aicodingcli.ai.providers  Test com.aicodingcli.ai.providers  assertEquals com.aicodingcli.ai.providers  assertFalse com.aicodingcli.ai.providers  
assertNotNull com.aicodingcli.ai.providers  assertThrows com.aicodingcli.ai.providers  
assertTrue com.aicodingcli.ai.providers  
claudeService com.aicodingcli.ai.providers  coEvery com.aicodingcli.ai.providers  coVerify com.aicodingcli.ai.providers  com com.aicodingcli.ai.providers  config com.aicodingcli.ai.providers  contains com.aicodingcli.ai.providers  	emptyList com.aicodingcli.ai.providers  listOf com.aicodingcli.ai.providers  mapOf com.aicodingcli.ai.providers  mockHttpClient com.aicodingcli.ai.providers  mockk com.aicodingcli.ai.providers  
ollamaService com.aicodingcli.ai.providers  
openAiService com.aicodingcli.ai.providers  runTest com.aicodingcli.ai.providers  to com.aicodingcli.ai.providers  
trimIndent com.aicodingcli.ai.providers  chat *com.aicodingcli.ai.providers.ClaudeService  
streamChat *com.aicodingcli.ai.providers.ClaudeService  testConnection *com.aicodingcli.ai.providers.ClaudeService  	AiMessage .com.aicodingcli.ai.providers.ClaudeServiceTest  
AiProvider .com.aicodingcli.ai.providers.ClaudeServiceTest  	AiRequest .com.aicodingcli.ai.providers.ClaudeServiceTest  AiServiceConfig .com.aicodingcli.ai.providers.ClaudeServiceTest  
ClaudeService .com.aicodingcli.ai.providers.ClaudeServiceTest  FinishReason .com.aicodingcli.ai.providers.ClaudeServiceTest  HttpResponse .com.aicodingcli.ai.providers.ClaudeServiceTest  HttpStatusCode .com.aicodingcli.ai.providers.ClaudeServiceTest  MessageRole .com.aicodingcli.ai.providers.ClaudeServiceTest  assertEquals .com.aicodingcli.ai.providers.ClaudeServiceTest  assertFalse .com.aicodingcli.ai.providers.ClaudeServiceTest  
assertNotNull .com.aicodingcli.ai.providers.ClaudeServiceTest  assertThrows .com.aicodingcli.ai.providers.ClaudeServiceTest  
assertTrue .com.aicodingcli.ai.providers.ClaudeServiceTest  
claudeService .com.aicodingcli.ai.providers.ClaudeServiceTest  coEvery .com.aicodingcli.ai.providers.ClaudeServiceTest  coVerify .com.aicodingcli.ai.providers.ClaudeServiceTest  com .com.aicodingcli.ai.providers.ClaudeServiceTest  config .com.aicodingcli.ai.providers.ClaudeServiceTest  contains .com.aicodingcli.ai.providers.ClaudeServiceTest  	emptyList .com.aicodingcli.ai.providers.ClaudeServiceTest  listOf .com.aicodingcli.ai.providers.ClaudeServiceTest  mapOf .com.aicodingcli.ai.providers.ClaudeServiceTest  mockHttpClient .com.aicodingcli.ai.providers.ClaudeServiceTest  mockk .com.aicodingcli.ai.providers.ClaudeServiceTest  runTest .com.aicodingcli.ai.providers.ClaudeServiceTest  to .com.aicodingcli.ai.providers.ClaudeServiceTest  
trimIndent .com.aicodingcli.ai.providers.ClaudeServiceTest  chat *com.aicodingcli.ai.providers.OllamaService  
streamChat *com.aicodingcli.ai.providers.OllamaService  testConnection *com.aicodingcli.ai.providers.OllamaService  	AiMessage .com.aicodingcli.ai.providers.OllamaServiceTest  
AiProvider .com.aicodingcli.ai.providers.OllamaServiceTest  	AiRequest .com.aicodingcli.ai.providers.OllamaServiceTest  AiServiceConfig .com.aicodingcli.ai.providers.OllamaServiceTest  FinishReason .com.aicodingcli.ai.providers.OllamaServiceTest  HttpResponse .com.aicodingcli.ai.providers.OllamaServiceTest  HttpStatusCode .com.aicodingcli.ai.providers.OllamaServiceTest  MessageRole .com.aicodingcli.ai.providers.OllamaServiceTest  
OllamaService .com.aicodingcli.ai.providers.OllamaServiceTest  assertEquals .com.aicodingcli.ai.providers.OllamaServiceTest  assertFalse .com.aicodingcli.ai.providers.OllamaServiceTest  
assertNotNull .com.aicodingcli.ai.providers.OllamaServiceTest  assertThrows .com.aicodingcli.ai.providers.OllamaServiceTest  
assertTrue .com.aicodingcli.ai.providers.OllamaServiceTest  coEvery .com.aicodingcli.ai.providers.OllamaServiceTest  coVerify .com.aicodingcli.ai.providers.OllamaServiceTest  com .com.aicodingcli.ai.providers.OllamaServiceTest  config .com.aicodingcli.ai.providers.OllamaServiceTest  contains .com.aicodingcli.ai.providers.OllamaServiceTest  	emptyList .com.aicodingcli.ai.providers.OllamaServiceTest  listOf .com.aicodingcli.ai.providers.OllamaServiceTest  mapOf .com.aicodingcli.ai.providers.OllamaServiceTest  mockHttpClient .com.aicodingcli.ai.providers.OllamaServiceTest  mockk .com.aicodingcli.ai.providers.OllamaServiceTest  
ollamaService .com.aicodingcli.ai.providers.OllamaServiceTest  runTest .com.aicodingcli.ai.providers.OllamaServiceTest  to .com.aicodingcli.ai.providers.OllamaServiceTest  
trimIndent .com.aicodingcli.ai.providers.OllamaServiceTest  chat *com.aicodingcli.ai.providers.OpenAiService  
streamChat *com.aicodingcli.ai.providers.OpenAiService  testConnection *com.aicodingcli.ai.providers.OpenAiService  	AiMessage .com.aicodingcli.ai.providers.OpenAiServiceTest  
AiProvider .com.aicodingcli.ai.providers.OpenAiServiceTest  	AiRequest .com.aicodingcli.ai.providers.OpenAiServiceTest  AiServiceConfig .com.aicodingcli.ai.providers.OpenAiServiceTest  FinishReason .com.aicodingcli.ai.providers.OpenAiServiceTest  HttpResponse .com.aicodingcli.ai.providers.OpenAiServiceTest  HttpStatusCode .com.aicodingcli.ai.providers.OpenAiServiceTest  MessageRole .com.aicodingcli.ai.providers.OpenAiServiceTest  
OpenAiService .com.aicodingcli.ai.providers.OpenAiServiceTest  assertEquals .com.aicodingcli.ai.providers.OpenAiServiceTest  assertFalse .com.aicodingcli.ai.providers.OpenAiServiceTest  
assertNotNull .com.aicodingcli.ai.providers.OpenAiServiceTest  assertThrows .com.aicodingcli.ai.providers.OpenAiServiceTest  
assertTrue .com.aicodingcli.ai.providers.OpenAiServiceTest  coEvery .com.aicodingcli.ai.providers.OpenAiServiceTest  coVerify .com.aicodingcli.ai.providers.OpenAiServiceTest  com .com.aicodingcli.ai.providers.OpenAiServiceTest  config .com.aicodingcli.ai.providers.OpenAiServiceTest  contains .com.aicodingcli.ai.providers.OpenAiServiceTest  	emptyList .com.aicodingcli.ai.providers.OpenAiServiceTest  listOf .com.aicodingcli.ai.providers.OpenAiServiceTest  mapOf .com.aicodingcli.ai.providers.OpenAiServiceTest  mockHttpClient .com.aicodingcli.ai.providers.OpenAiServiceTest  mockk .com.aicodingcli.ai.providers.OpenAiServiceTest  
openAiService .com.aicodingcli.ai.providers.OpenAiServiceTest  runTest .com.aicodingcli.ai.providers.OpenAiServiceTest  to .com.aicodingcli.ai.providers.OpenAiServiceTest  
trimIndent .com.aicodingcli.ai.providers.OpenAiServiceTest  	DebugTest com.aicodingcli.code  DefaultCodeAnalyzer com.aicodingcli.code  File com.aicodingcli.code  ProgrammingLanguage com.aicodingcli.code  TempDir com.aicodingcli.code  Test com.aicodingcli.code  analyzer com.aicodingcli.code  forEach com.aicodingcli.code  println com.aicodingcli.code  runBlocking com.aicodingcli.code  tempDir com.aicodingcli.code  
trimIndent com.aicodingcli.code  	writeText com.aicodingcli.code  DefaultCodeAnalyzer com.aicodingcli.code.DebugTest  File com.aicodingcli.code.DebugTest  ProgrammingLanguage com.aicodingcli.code.DebugTest  analyzer com.aicodingcli.code.DebugTest  println com.aicodingcli.code.DebugTest  runBlocking com.aicodingcli.code.DebugTest  tempDir com.aicodingcli.code.DebugTest  
trimIndent com.aicodingcli.code.DebugTest  	writeText com.aicodingcli.code.DebugTest  CodeAnalysisModelsTest com.aicodingcli.code.analysis  CodeAnalysisResult com.aicodingcli.code.analysis  CodeAnalyzerTest com.aicodingcli.code.analysis  	CodeIssue com.aicodingcli.code.analysis  CodeMetrics com.aicodingcli.code.analysis  DefaultCodeAnalyzer com.aicodingcli.code.analysis  
Dependency com.aicodingcli.code.analysis  DependencyScope com.aicodingcli.code.analysis  DependencyType com.aicodingcli.code.analysis  File com.aicodingcli.code.analysis  IllegalArgumentException com.aicodingcli.code.analysis  Improvement com.aicodingcli.code.analysis  ImprovementPriority com.aicodingcli.code.analysis  ImprovementType com.aicodingcli.code.analysis  
IssueSeverity com.aicodingcli.code.analysis  	IssueType com.aicodingcli.code.analysis  ProgrammingLanguage com.aicodingcli.code.analysis  ProjectAnalysisResult com.aicodingcli.code.analysis  String com.aicodingcli.code.analysis  TempDir com.aicodingcli.code.analysis  Test com.aicodingcli.code.analysis  analyzer com.aicodingcli.code.analysis  any com.aicodingcli.code.analysis  assertEquals com.aicodingcli.code.analysis  
assertNotNull com.aicodingcli.code.analysis  assertThrows com.aicodingcli.code.analysis  
assertTrue com.aicodingcli.code.analysis  createTempFile com.aicodingcli.code.analysis  	emptyList com.aicodingcli.code.analysis  
isNotEmpty com.aicodingcli.code.analysis  listOf com.aicodingcli.code.analysis  runBlocking com.aicodingcli.code.analysis  tempDir com.aicodingcli.code.analysis  
trimIndent com.aicodingcli.code.analysis  	writeText com.aicodingcli.code.analysis  CodeAnalysisResult 4com.aicodingcli.code.analysis.CodeAnalysisModelsTest  	CodeIssue 4com.aicodingcli.code.analysis.CodeAnalysisModelsTest  CodeMetrics 4com.aicodingcli.code.analysis.CodeAnalysisModelsTest  
Dependency 4com.aicodingcli.code.analysis.CodeAnalysisModelsTest  DependencyScope 4com.aicodingcli.code.analysis.CodeAnalysisModelsTest  DependencyType 4com.aicodingcli.code.analysis.CodeAnalysisModelsTest  Improvement 4com.aicodingcli.code.analysis.CodeAnalysisModelsTest  ImprovementPriority 4com.aicodingcli.code.analysis.CodeAnalysisModelsTest  ImprovementType 4com.aicodingcli.code.analysis.CodeAnalysisModelsTest  
IssueSeverity 4com.aicodingcli.code.analysis.CodeAnalysisModelsTest  	IssueType 4com.aicodingcli.code.analysis.CodeAnalysisModelsTest  ProgrammingLanguage 4com.aicodingcli.code.analysis.CodeAnalysisModelsTest  assertEquals 4com.aicodingcli.code.analysis.CodeAnalysisModelsTest  
assertTrue 4com.aicodingcli.code.analysis.CodeAnalysisModelsTest  	emptyList 4com.aicodingcli.code.analysis.CodeAnalysisModelsTest  listOf 4com.aicodingcli.code.analysis.CodeAnalysisModelsTest  dependencies 0com.aicodingcli.code.analysis.CodeAnalysisResult  filePath 0com.aicodingcli.code.analysis.CodeAnalysisResult  issues 0com.aicodingcli.code.analysis.CodeAnalysisResult  language 0com.aicodingcli.code.analysis.CodeAnalysisResult  metrics 0com.aicodingcli.code.analysis.CodeAnalysisResult  suggestions 0com.aicodingcli.code.analysis.CodeAnalysisResult  DefaultCodeAnalyzer .com.aicodingcli.code.analysis.CodeAnalyzerTest  File .com.aicodingcli.code.analysis.CodeAnalyzerTest  ImprovementType .com.aicodingcli.code.analysis.CodeAnalyzerTest  	IssueType .com.aicodingcli.code.analysis.CodeAnalyzerTest  ProgrammingLanguage .com.aicodingcli.code.analysis.CodeAnalyzerTest  analyzer .com.aicodingcli.code.analysis.CodeAnalyzerTest  any .com.aicodingcli.code.analysis.CodeAnalyzerTest  assertEquals .com.aicodingcli.code.analysis.CodeAnalyzerTest  
assertNotNull .com.aicodingcli.code.analysis.CodeAnalyzerTest  assertThrows .com.aicodingcli.code.analysis.CodeAnalyzerTest  
assertTrue .com.aicodingcli.code.analysis.CodeAnalyzerTest  createTempFile .com.aicodingcli.code.analysis.CodeAnalyzerTest  
isNotEmpty .com.aicodingcli.code.analysis.CodeAnalyzerTest  runBlocking .com.aicodingcli.code.analysis.CodeAnalyzerTest  tempDir .com.aicodingcli.code.analysis.CodeAnalyzerTest  
trimIndent .com.aicodingcli.code.analysis.CodeAnalyzerTest  	writeText .com.aicodingcli.code.analysis.CodeAnalyzerTest  column 'com.aicodingcli.code.analysis.CodeIssue  line 'com.aicodingcli.code.analysis.CodeIssue  message 'com.aicodingcli.code.analysis.CodeIssue  severity 'com.aicodingcli.code.analysis.CodeIssue  
suggestion 'com.aicodingcli.code.analysis.CodeIssue  type 'com.aicodingcli.code.analysis.CodeIssue  cyclomaticComplexity )com.aicodingcli.code.analysis.CodeMetrics  duplicatedLines )com.aicodingcli.code.analysis.CodeMetrics  linesOfCode )com.aicodingcli.code.analysis.CodeMetrics  maintainabilityIndex )com.aicodingcli.code.analysis.CodeMetrics  testCoverage )com.aicodingcli.code.analysis.CodeMetrics  analyzeFile 1com.aicodingcli.code.analysis.DefaultCodeAnalyzer  analyzeProject 1com.aicodingcli.code.analysis.DefaultCodeAnalyzer  detectIssues 1com.aicodingcli.code.analysis.DefaultCodeAnalyzer  suggestImprovements 1com.aicodingcli.code.analysis.DefaultCodeAnalyzer  name (com.aicodingcli.code.analysis.Dependency  scope (com.aicodingcli.code.analysis.Dependency  type (com.aicodingcli.code.analysis.Dependency  version (com.aicodingcli.code.analysis.Dependency  COMPILE -com.aicodingcli.code.analysis.DependencyScope  EXTERNAL ,com.aicodingcli.code.analysis.DependencyType  description )com.aicodingcli.code.analysis.Improvement  line )com.aicodingcli.code.analysis.Improvement  priority )com.aicodingcli.code.analysis.Improvement  type )com.aicodingcli.code.analysis.Improvement  HIGH 1com.aicodingcli.code.analysis.ImprovementPriority  MAINTAINABILITY -com.aicodingcli.code.analysis.ImprovementType  PERFORMANCE -com.aicodingcli.code.analysis.ImprovementType  LOW +com.aicodingcli.code.analysis.IssueSeverity  MEDIUM +com.aicodingcli.code.analysis.IssueSeverity  
CODE_SMELL 'com.aicodingcli.code.analysis.IssueType  NAMING_CONVENTION 'com.aicodingcli.code.analysis.IssueType  UNUSED_CODE 'com.aicodingcli.code.analysis.IssueType  fileResults 3com.aicodingcli.code.analysis.ProjectAnalysisResult  overallMetrics 3com.aicodingcli.code.analysis.ProjectAnalysisResult  projectPath 3com.aicodingcli.code.analysis.ProjectAnalysisResult  IllegalArgumentException com.aicodingcli.code.common  ProgrammingLanguage com.aicodingcli.code.common  ProgrammingLanguageTest com.aicodingcli.code.common  Test com.aicodingcli.code.common  assertEquals com.aicodingcli.code.common  assertThrows com.aicodingcli.code.common  
assertTrue com.aicodingcli.code.common  fromFileExtension com.aicodingcli.code.common  fromFilePath com.aicodingcli.code.common  	lowercase com.aicodingcli.code.common  	Companion /com.aicodingcli.code.common.ProgrammingLanguage  JAVA /com.aicodingcli.code.common.ProgrammingLanguage  KOTLIN /com.aicodingcli.code.common.ProgrammingLanguage  PYTHON /com.aicodingcli.code.common.ProgrammingLanguage  
fileExtension /com.aicodingcli.code.common.ProgrammingLanguage  fromFileExtension /com.aicodingcli.code.common.ProgrammingLanguage  fromFilePath /com.aicodingcli.code.common.ProgrammingLanguage  name /com.aicodingcli.code.common.ProgrammingLanguage  supportsClasses /com.aicodingcli.code.common.ProgrammingLanguage  supportsInterfaces /com.aicodingcli.code.common.ProgrammingLanguage  fromFileExtension 9com.aicodingcli.code.common.ProgrammingLanguage.Companion  fromFilePath 9com.aicodingcli.code.common.ProgrammingLanguage.Companion  ProgrammingLanguage 3com.aicodingcli.code.common.ProgrammingLanguageTest  assertEquals 3com.aicodingcli.code.common.ProgrammingLanguageTest  assertThrows 3com.aicodingcli.code.common.ProgrammingLanguageTest  
assertTrue 3com.aicodingcli.code.common.ProgrammingLanguageTest  fromFileExtension 3com.aicodingcli.code.common.ProgrammingLanguageTest  fromFilePath 3com.aicodingcli.code.common.ProgrammingLanguageTest  	lowercase 3com.aicodingcli.code.common.ProgrammingLanguageTest  ImprovementType com.aicodingcli.code.quality  ProgrammingLanguage com.aicodingcli.code.quality  QualityAnalyzer com.aicodingcli.code.quality  QualityAnalyzerTest com.aicodingcli.code.quality  Test com.aicodingcli.code.quality  any com.aicodingcli.code.quality  
assertTrue com.aicodingcli.code.quality  forEach com.aicodingcli.code.quality  
isNotEmpty com.aicodingcli.code.quality  println com.aicodingcli.code.quality  
trimIndent com.aicodingcli.code.quality  suggestImprovements ,com.aicodingcli.code.quality.QualityAnalyzer  ImprovementType 0com.aicodingcli.code.quality.QualityAnalyzerTest  ProgrammingLanguage 0com.aicodingcli.code.quality.QualityAnalyzerTest  QualityAnalyzer 0com.aicodingcli.code.quality.QualityAnalyzerTest  analyzer 0com.aicodingcli.code.quality.QualityAnalyzerTest  any 0com.aicodingcli.code.quality.QualityAnalyzerTest  
assertTrue 0com.aicodingcli.code.quality.QualityAnalyzerTest  
isNotEmpty 0com.aicodingcli.code.quality.QualityAnalyzerTest  println 0com.aicodingcli.code.quality.QualityAnalyzerTest  
trimIndent 0com.aicodingcli.code.quality.QualityAnalyzerTest  
AiProvider com.aicodingcli.config  	AppConfig com.aicodingcli.config  
BeforeEach com.aicodingcli.config  
ConfigManager com.aicodingcli.config  ConfigManagerTest com.aicodingcli.config  File com.aicodingcli.config  TempDir com.aicodingcli.config  Test com.aicodingcli.config  assertEquals com.aicodingcli.config  
assertNotNull com.aicodingcli.config  
assertTrue com.aicodingcli.config  com com.aicodingcli.config  
configManager com.aicodingcli.config  
isNotEmpty com.aicodingcli.config  mapOf com.aicodingcli.config  runTest com.aicodingcli.config  tempDir com.aicodingcli.config  to com.aicodingcli.config  
trimIndent com.aicodingcli.config  	writeText com.aicodingcli.config  defaultProvider  com.aicodingcli.config.AppConfig  	providers  com.aicodingcli.config.AppConfig  getCurrentProviderConfig $com.aicodingcli.config.ConfigManager  
loadConfig $com.aicodingcli.config.ConfigManager  
saveConfig $com.aicodingcli.config.ConfigManager  setDefaultProvider $com.aicodingcli.config.ConfigManager  updateProviderConfig $com.aicodingcli.config.ConfigManager  
AiProvider (com.aicodingcli.config.ConfigManagerTest  	AppConfig (com.aicodingcli.config.ConfigManagerTest  
ConfigManager (com.aicodingcli.config.ConfigManagerTest  File (com.aicodingcli.config.ConfigManagerTest  assertEquals (com.aicodingcli.config.ConfigManagerTest  
assertNotNull (com.aicodingcli.config.ConfigManagerTest  
assertTrue (com.aicodingcli.config.ConfigManagerTest  com (com.aicodingcli.config.ConfigManagerTest  
configManager (com.aicodingcli.config.ConfigManagerTest  
isNotEmpty (com.aicodingcli.config.ConfigManagerTest  mapOf (com.aicodingcli.config.ConfigManagerTest  runTest (com.aicodingcli.config.ConfigManagerTest  tempDir (com.aicodingcli.config.ConfigManagerTest  to (com.aicodingcli.config.ConfigManagerTest  
trimIndent (com.aicodingcli.config.ConfigManagerTest  	writeText (com.aicodingcli.config.ConfigManagerTest  	AfterEach com.aicodingcli.history  
AiProvider com.aicodingcli.history  
BeforeEach com.aicodingcli.history  ConversationMessage com.aicodingcli.history  ConversationSession com.aicodingcli.history  File com.aicodingcli.history  Files com.aicodingcli.history  HistoryManager com.aicodingcli.history  HistoryManagerTest com.aicodingcli.history  HistoryModelsTest com.aicodingcli.history  HistorySearchCriteria com.aicodingcli.history  HistoryStatistics com.aicodingcli.history  IllegalArgumentException com.aicodingcli.history  Instant com.aicodingcli.history  MessageRole com.aicodingcli.history  MessageTokenUsage com.aicodingcli.history  Test com.aicodingcli.history  Thread com.aicodingcli.history  all com.aicodingcli.history  assertEquals com.aicodingcli.history  assertFalse com.aicodingcli.history  
assertNotNull com.aicodingcli.history  
assertNull com.aicodingcli.history  assertThrows com.aicodingcli.history  
assertTrue com.aicodingcli.history  contains com.aicodingcli.history  deleteRecursively com.aicodingcli.history  emptyMap com.aicodingcli.history  mapOf com.aicodingcli.history  repeat com.aicodingcli.history  take com.aicodingcli.history  to com.aicodingcli.history  content +com.aicodingcli.history.ConversationMessage  id +com.aicodingcli.history.ConversationMessage  role +com.aicodingcli.history.ConversationMessage  	timestamp +com.aicodingcli.history.ConversationMessage  
tokenUsage +com.aicodingcli.history.ConversationMessage  
addMessage +com.aicodingcli.history.ConversationSession  	createdAt +com.aicodingcli.history.ConversationSession  getLastAssistantMessage +com.aicodingcli.history.ConversationSession  getLastUserMessage +com.aicodingcli.history.ConversationSession  
getSummary +com.aicodingcli.history.ConversationSession  id +com.aicodingcli.history.ConversationSession  messages +com.aicodingcli.history.ConversationSession  model +com.aicodingcli.history.ConversationSession  provider +com.aicodingcli.history.ConversationSession  title +com.aicodingcli.history.ConversationSession  	updatedAt +com.aicodingcli.history.ConversationSession  
addMessage &com.aicodingcli.history.HistoryManager  clearAllConversations &com.aicodingcli.history.HistoryManager  createConversation &com.aicodingcli.history.HistoryManager  deleteConversation &com.aicodingcli.history.HistoryManager  getAllConversations &com.aicodingcli.history.HistoryManager  getConversation &com.aicodingcli.history.HistoryManager  
getStatistics &com.aicodingcli.history.HistoryManager  searchConversations &com.aicodingcli.history.HistoryManager  
AiProvider *com.aicodingcli.history.HistoryManagerTest  Files *com.aicodingcli.history.HistoryManagerTest  HistoryManager *com.aicodingcli.history.HistoryManagerTest  HistorySearchCriteria *com.aicodingcli.history.HistoryManagerTest  MessageRole *com.aicodingcli.history.HistoryManagerTest  MessageTokenUsage *com.aicodingcli.history.HistoryManagerTest  Thread *com.aicodingcli.history.HistoryManagerTest  all *com.aicodingcli.history.HistoryManagerTest  assertEquals *com.aicodingcli.history.HistoryManagerTest  assertFalse *com.aicodingcli.history.HistoryManagerTest  
assertNotNull *com.aicodingcli.history.HistoryManagerTest  
assertNull *com.aicodingcli.history.HistoryManagerTest  assertThrows *com.aicodingcli.history.HistoryManagerTest  
assertTrue *com.aicodingcli.history.HistoryManagerTest  contains *com.aicodingcli.history.HistoryManagerTest  deleteRecursively *com.aicodingcli.history.HistoryManagerTest  historyManager *com.aicodingcli.history.HistoryManagerTest  repeat *com.aicodingcli.history.HistoryManagerTest  take *com.aicodingcli.history.HistoryManagerTest  tempDir *com.aicodingcli.history.HistoryManagerTest  
AiProvider )com.aicodingcli.history.HistoryModelsTest  ConversationMessage )com.aicodingcli.history.HistoryModelsTest  ConversationSession )com.aicodingcli.history.HistoryModelsTest  HistorySearchCriteria )com.aicodingcli.history.HistoryModelsTest  HistoryStatistics )com.aicodingcli.history.HistoryModelsTest  Instant )com.aicodingcli.history.HistoryModelsTest  MessageRole )com.aicodingcli.history.HistoryModelsTest  MessageTokenUsage )com.aicodingcli.history.HistoryModelsTest  assertEquals )com.aicodingcli.history.HistoryModelsTest  
assertNotNull )com.aicodingcli.history.HistoryModelsTest  
assertNull )com.aicodingcli.history.HistoryModelsTest  assertThrows )com.aicodingcli.history.HistoryModelsTest  
assertTrue )com.aicodingcli.history.HistoryModelsTest  contains )com.aicodingcli.history.HistoryModelsTest  emptyMap )com.aicodingcli.history.HistoryModelsTest  mapOf )com.aicodingcli.history.HistoryModelsTest  to )com.aicodingcli.history.HistoryModelsTest  fromDate -com.aicodingcli.history.HistorySearchCriteria  limit -com.aicodingcli.history.HistorySearchCriteria  model -com.aicodingcli.history.HistorySearchCriteria  provider -com.aicodingcli.history.HistorySearchCriteria  query -com.aicodingcli.history.HistorySearchCriteria  toDate -com.aicodingcli.history.HistorySearchCriteria  newestConversation )com.aicodingcli.history.HistoryStatistics  oldestConversation )com.aicodingcli.history.HistoryStatistics  providerBreakdown )com.aicodingcli.history.HistoryStatistics  totalConversations )com.aicodingcli.history.HistoryStatistics  
totalMessages )com.aicodingcli.history.HistoryStatistics  totalTokensUsed )com.aicodingcli.history.HistoryStatistics  completionTokens )com.aicodingcli.history.MessageTokenUsage  promptTokens )com.aicodingcli.history.MessageTokenUsage  totalTokens )com.aicodingcli.history.MessageTokenUsage  AiHttpClient com.aicodingcli.http  ByteReadChannel com.aicodingcli.http  	Exception com.aicodingcli.http  HttpClientTest com.aicodingcli.http  
HttpException com.aicodingcli.http  HttpHeaders com.aicodingcli.http  
HttpMethod com.aicodingcli.http  HttpResponse com.aicodingcli.http  HttpStatusCode com.aicodingcli.http  
MockEngine com.aicodingcli.http  RetryConfig com.aicodingcli.http  Test com.aicodingcli.http  assertEquals com.aicodingcli.http  assertThrows com.aicodingcli.http  	headersOf com.aicodingcli.http  kotlinx com.aicodingcli.http  mapOf com.aicodingcli.http  respond com.aicodingcli.http  runTest com.aicodingcli.http  to com.aicodingcli.http  get !com.aicodingcli.http.AiHttpClient  post !com.aicodingcli.http.AiHttpClient  AiHttpClient #com.aicodingcli.http.HttpClientTest  ByteReadChannel #com.aicodingcli.http.HttpClientTest  	Exception #com.aicodingcli.http.HttpClientTest  HttpHeaders #com.aicodingcli.http.HttpClientTest  
HttpMethod #com.aicodingcli.http.HttpClientTest  HttpStatusCode #com.aicodingcli.http.HttpClientTest  
MockEngine #com.aicodingcli.http.HttpClientTest  RetryConfig #com.aicodingcli.http.HttpClientTest  assertEquals #com.aicodingcli.http.HttpClientTest  assertThrows #com.aicodingcli.http.HttpClientTest  	headersOf #com.aicodingcli.http.HttpClientTest  kotlinx #com.aicodingcli.http.HttpClientTest  mapOf #com.aicodingcli.http.HttpClientTest  respond #com.aicodingcli.http.HttpClientTest  runTest #com.aicodingcli.http.HttpClientTest  to #com.aicodingcli.http.HttpClientTest  body !com.aicodingcli.http.HttpResponse  headers !com.aicodingcli.http.HttpResponse  status !com.aicodingcli.http.HttpResponse  AiHttpClient io.ktor.client.engine.mock  ByteReadChannel io.ktor.client.engine.mock  	Exception io.ktor.client.engine.mock  
HttpException io.ktor.client.engine.mock  HttpHeaders io.ktor.client.engine.mock  
HttpMethod io.ktor.client.engine.mock  HttpStatusCode io.ktor.client.engine.mock  
MockEngine io.ktor.client.engine.mock  MockRequestHandleScope io.ktor.client.engine.mock  RetryConfig io.ktor.client.engine.mock  Test io.ktor.client.engine.mock  assertEquals io.ktor.client.engine.mock  assertThrows io.ktor.client.engine.mock  	headersOf io.ktor.client.engine.mock  kotlinx io.ktor.client.engine.mock  mapOf io.ktor.client.engine.mock  respond io.ktor.client.engine.mock  runTest io.ktor.client.engine.mock  to io.ktor.client.engine.mock  	Companion %io.ktor.client.engine.mock.MockEngine  invoke %io.ktor.client.engine.mock.MockEngine  invoke /io.ktor.client.engine.mock.MockEngine.Companion  ByteReadChannel 1io.ktor.client.engine.mock.MockRequestHandleScope  	Exception 1io.ktor.client.engine.mock.MockRequestHandleScope  HttpHeaders 1io.ktor.client.engine.mock.MockRequestHandleScope  
HttpMethod 1io.ktor.client.engine.mock.MockRequestHandleScope  HttpStatusCode 1io.ktor.client.engine.mock.MockRequestHandleScope  assertEquals 1io.ktor.client.engine.mock.MockRequestHandleScope  	headersOf 1io.ktor.client.engine.mock.MockRequestHandleScope  kotlinx 1io.ktor.client.engine.mock.MockRequestHandleScope  respond 1io.ktor.client.engine.mock.MockRequestHandleScope  HttpRequestData io.ktor.client.request  HttpResponseData io.ktor.client.request  body &io.ktor.client.request.HttpRequestData  headers &io.ktor.client.request.HttpRequestData  method &io.ktor.client.request.HttpRequestData  AiHttpClient io.ktor.http  	AiMessage io.ktor.http  
AiProvider io.ktor.http  	AiRequest io.ktor.http  AiServiceConfig io.ktor.http  
BeforeEach io.ktor.http  ByteReadChannel io.ktor.http  ClaudeException io.ktor.http  
ClaudeService io.ktor.http  ContentType io.ktor.http  	Exception io.ktor.http  FinishReason io.ktor.http  Headers io.ktor.http  
HttpException io.ktor.http  HttpHeaders io.ktor.http  
HttpMethod io.ktor.http  HttpResponse io.ktor.http  HttpStatusCode io.ktor.http  IllegalArgumentException io.ktor.http  MessageRole io.ktor.http  
MockEngine io.ktor.http  OllamaException io.ktor.http  
OllamaService io.ktor.http  OpenAiException io.ktor.http  
OpenAiService io.ktor.http  RetryConfig io.ktor.http  Test io.ktor.http  assertEquals io.ktor.http  assertFalse io.ktor.http  
assertNotNull io.ktor.http  assertThrows io.ktor.http  
assertTrue io.ktor.http  
claudeService io.ktor.http  coEvery io.ktor.http  coVerify io.ktor.http  com io.ktor.http  config io.ktor.http  contains io.ktor.http  	emptyList io.ktor.http  	headersOf io.ktor.http  kotlinx io.ktor.http  listOf io.ktor.http  mapOf io.ktor.http  mockHttpClient io.ktor.http  mockk io.ktor.http  
ollamaService io.ktor.http  
openAiService io.ktor.http  respond io.ktor.http  runTest io.ktor.http  to io.ktor.http  
trimIndent io.ktor.http  toString io.ktor.http.ContentType  toString &io.ktor.http.HeaderValueWithParameters  get io.ktor.http.Headers  ContentType io.ktor.http.HttpHeaders  	Companion io.ktor.http.HttpMethod  Post io.ktor.http.HttpMethod  Post !io.ktor.http.HttpMethod.Companion  	Companion io.ktor.http.HttpStatusCode  Created io.ktor.http.HttpStatusCode  NotFound io.ktor.http.HttpStatusCode  OK io.ktor.http.HttpStatusCode  ServiceUnavailable io.ktor.http.HttpStatusCode  TooManyRequests io.ktor.http.HttpStatusCode  Unauthorized io.ktor.http.HttpStatusCode  Created %io.ktor.http.HttpStatusCode.Companion  NotFound %io.ktor.http.HttpStatusCode.Companion  OK %io.ktor.http.HttpStatusCode.Companion  ServiceUnavailable %io.ktor.http.HttpStatusCode.Companion  TooManyRequests %io.ktor.http.HttpStatusCode.Companion  Unauthorized %io.ktor.http.HttpStatusCode.Companion  OutgoingContent io.ktor.http.content  contentType $io.ktor.http.content.OutgoingContent  get io.ktor.util.StringValues  AiHttpClient io.ktor.utils.io  ByteReadChannel io.ktor.utils.io  	Exception io.ktor.utils.io  
HttpException io.ktor.utils.io  HttpHeaders io.ktor.utils.io  
HttpMethod io.ktor.utils.io  HttpStatusCode io.ktor.utils.io  
MockEngine io.ktor.utils.io  RetryConfig io.ktor.utils.io  Test io.ktor.utils.io  assertEquals io.ktor.utils.io  assertThrows io.ktor.utils.io  	headersOf io.ktor.utils.io  kotlinx io.ktor.utils.io  mapOf io.ktor.utils.io  respond io.ktor.utils.io  runTest io.ktor.utils.io  to io.ktor.utils.io  AiHttpClient io.mockk  	AiMessage io.mockk  
AiProvider io.mockk  	AiRequest io.mockk  AiServiceConfig io.mockk  
BeforeEach io.mockk  ClaudeException io.mockk  
ClaudeService io.mockk  FinishReason io.mockk  HttpResponse io.mockk  HttpStatusCode io.mockk  IllegalArgumentException io.mockk  MessageRole io.mockk  MockKAdditionalAnswerScope io.mockk  MockKMatcherScope io.mockk  MockKStubScope io.mockk  MockKVerificationScope io.mockk  OllamaException io.mockk  
OllamaService io.mockk  OpenAiException io.mockk  
OpenAiService io.mockk  Test io.mockk  assertEquals io.mockk  assertFalse io.mockk  
assertNotNull io.mockk  assertThrows io.mockk  
assertTrue io.mockk  
claudeService io.mockk  coEvery io.mockk  coVerify io.mockk  com io.mockk  config io.mockk  contains io.mockk  	emptyList io.mockk  listOf io.mockk  mapOf io.mockk  mockHttpClient io.mockk  mockk io.mockk  
ollamaService io.mockk  
openAiService io.mockk  runTest io.mockk  to io.mockk  
trimIndent io.mockk  any io.mockk.MockKMatcherScope  match io.mockk.MockKMatcherScope  mockHttpClient io.mockk.MockKMatcherScope  returns io.mockk.MockKStubScope  throws io.mockk.MockKStubScope  any io.mockk.MockKVerificationScope  contains io.mockk.MockKVerificationScope  match io.mockk.MockKVerificationScope  mockHttpClient io.mockk.MockKVerificationScope  ByteArrayOutputStream java.io  File java.io  PrintStream java.io  toString java.io.ByteArrayOutputStream  absolutePath java.io.File  deleteRecursively java.io.File  exists java.io.File  	writeText java.io.File  	Exception 	java.lang  IllegalArgumentException 	java.lang  out java.lang.System  setOut java.lang.System  sleep java.lang.Thread  Files 
java.nio.file  createTempDirectory java.nio.file.Files  toFile java.nio.file.Path  Instant 	java.time  epochSecond java.time.Instant  now java.time.Instant  Array kotlin  CharSequence kotlin  	Exception kotlin  	Function0 kotlin  	Function1 kotlin  IllegalArgumentException kotlin  Nothing kotlin  Pair kotlin  arrayOf kotlin  repeat kotlin  to kotlin  message kotlin.IllegalArgumentException  	compareTo 
kotlin.Int  inc 
kotlin.Int  	compareTo kotlin.Long  minus kotlin.Long  contains 
kotlin.String  	lowercase 
kotlin.String  take 
kotlin.String  to 
kotlin.String  trim 
kotlin.String  
trimIndent 
kotlin.String  message kotlin.Throwable  List kotlin.collections  Map kotlin.collections  MutableList kotlin.collections  all kotlin.collections  any kotlin.collections  contains kotlin.collections  	emptyList kotlin.collections  emptyMap kotlin.collections  forEach kotlin.collections  
isNotEmpty kotlin.collections  listOf kotlin.collections  mapOf kotlin.collections  
mutableListOf kotlin.collections  take kotlin.collections  all kotlin.collections.List  any kotlin.collections.List  get kotlin.collections.List  isEmpty kotlin.collections.List  
isNotEmpty kotlin.collections.List  size kotlin.collections.List  get kotlin.collections.Map  isEmpty kotlin.collections.Map  
isNotEmpty kotlin.collections.Map  size kotlin.collections.Map  add kotlin.collections.MutableList  get kotlin.collections.MutableList  isEmpty kotlin.collections.MutableList  size kotlin.collections.MutableList  SuspendFunction1 kotlin.coroutines  SuspendFunction2 kotlin.coroutines  createTempFile 	kotlin.io  deleteRecursively 	kotlin.io  println 	kotlin.io  	writeText 	kotlin.io  contains 
kotlin.ranges  Sequence kotlin.sequences  all kotlin.sequences  any kotlin.sequences  contains kotlin.sequences  forEach kotlin.sequences  take kotlin.sequences  all kotlin.text  any kotlin.text  contains kotlin.text  forEach kotlin.text  
isNotEmpty kotlin.text  	lowercase kotlin.text  repeat kotlin.text  take kotlin.text  trim kotlin.text  
trimIndent kotlin.text  CoroutineScope kotlinx.coroutines  delay kotlinx.coroutines  runBlocking kotlinx.coroutines  File !kotlinx.coroutines.CoroutineScope  ImprovementType !kotlinx.coroutines.CoroutineScope  	IssueType !kotlinx.coroutines.CoroutineScope  ProgrammingLanguage !kotlinx.coroutines.CoroutineScope  analyzer !kotlinx.coroutines.CoroutineScope  any !kotlinx.coroutines.CoroutineScope  assertEquals !kotlinx.coroutines.CoroutineScope  
assertNotNull !kotlinx.coroutines.CoroutineScope  
assertTrue !kotlinx.coroutines.CoroutineScope  createTempFile !kotlinx.coroutines.CoroutineScope  
isNotEmpty !kotlinx.coroutines.CoroutineScope  println !kotlinx.coroutines.CoroutineScope  tempDir !kotlinx.coroutines.CoroutineScope  
trimIndent !kotlinx.coroutines.CoroutineScope  	writeText !kotlinx.coroutines.CoroutineScope  Flow kotlinx.coroutines.flow  
FlowCollector kotlinx.coroutines.flow  flowOf kotlinx.coroutines.flow  collect kotlinx.coroutines.flow.Flow  <SAM-CONSTRUCTOR> %kotlinx.coroutines.flow.FlowCollector  	TestScope kotlinx.coroutines.test  runTest kotlinx.coroutines.test  AiHttpClient !kotlinx.coroutines.test.TestScope  	AiMessage !kotlinx.coroutines.test.TestScope  
AiProvider !kotlinx.coroutines.test.TestScope  	AiRequest !kotlinx.coroutines.test.TestScope  AiServiceConfig !kotlinx.coroutines.test.TestScope  AiServiceFactory !kotlinx.coroutines.test.TestScope  
AiStreamChunk !kotlinx.coroutines.test.TestScope  	AppConfig !kotlinx.coroutines.test.TestScope  ByteReadChannel !kotlinx.coroutines.test.TestScope  
ClaudeService !kotlinx.coroutines.test.TestScope  	Exception !kotlinx.coroutines.test.TestScope  File !kotlinx.coroutines.test.TestScope  FinishReason !kotlinx.coroutines.test.TestScope  HttpHeaders !kotlinx.coroutines.test.TestScope  
HttpMethod !kotlinx.coroutines.test.TestScope  HttpResponse !kotlinx.coroutines.test.TestScope  HttpStatusCode !kotlinx.coroutines.test.TestScope  MessageRole !kotlinx.coroutines.test.TestScope  
MockEngine !kotlinx.coroutines.test.TestScope  
OllamaService !kotlinx.coroutines.test.TestScope  
OpenAiService !kotlinx.coroutines.test.TestScope  RetryConfig !kotlinx.coroutines.test.TestScope  assertDoesNotThrow !kotlinx.coroutines.test.TestScope  assertEquals !kotlinx.coroutines.test.TestScope  assertFalse !kotlinx.coroutines.test.TestScope  
assertNotNull !kotlinx.coroutines.test.TestScope  assertThrows !kotlinx.coroutines.test.TestScope  
assertTrue !kotlinx.coroutines.test.TestScope  
claudeService !kotlinx.coroutines.test.TestScope  coEvery !kotlinx.coroutines.test.TestScope  coVerify !kotlinx.coroutines.test.TestScope  com !kotlinx.coroutines.test.TestScope  config !kotlinx.coroutines.test.TestScope  
configManager !kotlinx.coroutines.test.TestScope  contains !kotlinx.coroutines.test.TestScope  
createService !kotlinx.coroutines.test.TestScope  	emptyList !kotlinx.coroutines.test.TestScope  	headersOf !kotlinx.coroutines.test.TestScope  
isNotEmpty !kotlinx.coroutines.test.TestScope  kotlinx !kotlinx.coroutines.test.TestScope  listOf !kotlinx.coroutines.test.TestScope  mapOf !kotlinx.coroutines.test.TestScope  mockHttpClient !kotlinx.coroutines.test.TestScope  mockk !kotlinx.coroutines.test.TestScope  
mutableListOf !kotlinx.coroutines.test.TestScope  
ollamaService !kotlinx.coroutines.test.TestScope  
openAiService !kotlinx.coroutines.test.TestScope  respond !kotlinx.coroutines.test.TestScope  tempDir !kotlinx.coroutines.test.TestScope  to !kotlinx.coroutines.test.TestScope  
trimIndent !kotlinx.coroutines.test.TestScope  	writeText !kotlinx.coroutines.test.TestScope  	AfterEach org.junit.jupiter.api  AiHttpClient org.junit.jupiter.api  	AiMessage org.junit.jupiter.api  
AiProvider org.junit.jupiter.api  	AiRequest org.junit.jupiter.api  
AiResponse org.junit.jupiter.api  	AiService org.junit.jupiter.api  AiServiceConfig org.junit.jupiter.api  AiServiceFactory org.junit.jupiter.api  
AiStreamChunk org.junit.jupiter.api  	AppConfig org.junit.jupiter.api  
BeforeEach org.junit.jupiter.api  ByteReadChannel org.junit.jupiter.api  ClaudeException org.junit.jupiter.api  
ClaudeService org.junit.jupiter.api  
ConfigManager org.junit.jupiter.api  ConversationMessage org.junit.jupiter.api  ConversationSession org.junit.jupiter.api  	Exception org.junit.jupiter.api  File org.junit.jupiter.api  Files org.junit.jupiter.api  FinishReason org.junit.jupiter.api  HistoryManager org.junit.jupiter.api  HistorySearchCriteria org.junit.jupiter.api  HistoryStatistics org.junit.jupiter.api  
HttpException org.junit.jupiter.api  HttpHeaders org.junit.jupiter.api  
HttpMethod org.junit.jupiter.api  HttpResponse org.junit.jupiter.api  HttpStatusCode org.junit.jupiter.api  IllegalArgumentException org.junit.jupiter.api  Instant org.junit.jupiter.api  MessageRole org.junit.jupiter.api  MessageTokenUsage org.junit.jupiter.api  
MockEngine org.junit.jupiter.api  OllamaException org.junit.jupiter.api  
OllamaService org.junit.jupiter.api  OpenAiException org.junit.jupiter.api  
OpenAiService org.junit.jupiter.api  RetryConfig org.junit.jupiter.api  TempDir org.junit.jupiter.api  Test org.junit.jupiter.api  Thread org.junit.jupiter.api  
TokenUsage org.junit.jupiter.api  all org.junit.jupiter.api  assertDoesNotThrow org.junit.jupiter.api  assertEquals org.junit.jupiter.api  assertFalse org.junit.jupiter.api  
assertNotNull org.junit.jupiter.api  
assertNull org.junit.jupiter.api  assertThrows org.junit.jupiter.api  
assertTrue org.junit.jupiter.api  
claudeService org.junit.jupiter.api  coEvery org.junit.jupiter.api  coVerify org.junit.jupiter.api  com org.junit.jupiter.api  config org.junit.jupiter.api  
configManager org.junit.jupiter.api  contains org.junit.jupiter.api  
createService org.junit.jupiter.api  deleteRecursively org.junit.jupiter.api  	emptyList org.junit.jupiter.api  emptyMap org.junit.jupiter.api  	headersOf org.junit.jupiter.api  
isNotEmpty org.junit.jupiter.api  kotlinx org.junit.jupiter.api  listOf org.junit.jupiter.api  mapOf org.junit.jupiter.api  mockHttpClient org.junit.jupiter.api  mockk org.junit.jupiter.api  
mutableListOf org.junit.jupiter.api  
ollamaService org.junit.jupiter.api  
openAiService org.junit.jupiter.api  repeat org.junit.jupiter.api  respond org.junit.jupiter.api  runTest org.junit.jupiter.api  take org.junit.jupiter.api  tempDir org.junit.jupiter.api  to org.junit.jupiter.api  
trimIndent org.junit.jupiter.api  	writeText org.junit.jupiter.api  assertDoesNotThrow  org.junit.jupiter.api.Assertions  assertEquals  org.junit.jupiter.api.Assertions  assertFalse  org.junit.jupiter.api.Assertions  
assertNotNull  org.junit.jupiter.api.Assertions  
assertNull  org.junit.jupiter.api.Assertions  assertThrows  org.junit.jupiter.api.Assertions  
assertTrue  org.junit.jupiter.api.Assertions  
Executable org.junit.jupiter.api.function  ThrowingSupplier org.junit.jupiter.api.function  <SAM-CONSTRUCTOR> )org.junit.jupiter.api.function.Executable  <SAM-CONSTRUCTOR> /org.junit.jupiter.api.function.ThrowingSupplier  TempDir org.junit.jupiter.api.io  	Exception com.aicodingcli.code  ProjectAnalysisDebugTest com.aicodingcli.code  DefaultCodeAnalyzer -com.aicodingcli.code.ProjectAnalysisDebugTest  File -com.aicodingcli.code.ProjectAnalysisDebugTest  analyzer -com.aicodingcli.code.ProjectAnalysisDebugTest  println -com.aicodingcli.code.ProjectAnalysisDebugTest  runBlocking -com.aicodingcli.code.ProjectAnalysisDebugTest  tempDir -com.aicodingcli.code.ProjectAnalysisDebugTest  
trimIndent -com.aicodingcli.code.ProjectAnalysisDebugTest  	writeText -com.aicodingcli.code.ProjectAnalysisDebugTest  AnalysisSummary com.aicodingcli.code.analysis  averageComplexity -com.aicodingcli.code.analysis.AnalysisSummary  overallMaintainabilityIndex -com.aicodingcli.code.analysis.AnalysisSummary  
totalFiles -com.aicodingcli.code.analysis.AnalysisSummary  summary 3com.aicodingcli.code.analysis.ProjectAnalysisResult  message kotlin.Exception  printStackTrace kotlin.Exception  printStackTrace kotlin.Throwable                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       